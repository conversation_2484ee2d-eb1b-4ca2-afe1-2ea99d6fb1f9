import dayjs from 'dayjs'
import { rebateTypeList, rebateCalculationFrequencyList } from './index'
import VxeRemoteSearch from '@/components/VxeRemoteSearch/index.vue'

export default {
  components: { VxeRemoteSearch },
  props: {
    detailInfo: {
      type: Object,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      isGf: false
    }
  },
  watch: {
    list: {
      handler(val) {
        let tableData = val
        this.type === 'detail' && (tableData = this.serializeList(val))
        this.tableData = tableData
      },
      deep: true,
      immediate: true
    },
    detailInfo: {
      handler() {
        this.getGfCompanyList()
      },
      deep: true,
      immediate: true

    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    toolbar() {
      switch (this.type) {
        case 'purcahseAttachment':
          return [
            {
              code: 'download',
              name: this.$t('下载'),
              icon: 'vxe-icon-download',
              status: ''
            }
          ]
        case 'supplierAttachment':
          return [
            {
              code: 'upload',
              name: this.$t('上传'),
              icon: 'vxe-icon-upload',
              status: 'primary',
              isHidden: this.detailInfo.status !== 2
            },
            {
              code: 'delete',
              name: this.$t('删除'),
              icon: 'vxe-icon-delete',
              status: 'primary',
              isHidden: this.detailInfo.status !== 2
            },
            {
              code: 'download',
              name: this.$t('下载'),
              icon: 'vxe-icon-download',
              status: ''
            }
          ]
        default:
          return []
      }
    },
    cellTools() {
      switch (this.type) {
        case 'purcahseAttachment':
          return ['download']
        case 'supplierAttachment':
          return ['download', 'delete']
        default:
          return []
      }
    },
    columns() {
      const isGf = this.isGf
      switch (this.type) {
        case 'detail':
          return [
            {
              field: 'lineNumber',
              title: this.$t('行号'),
              width: 50
            },
            {
              field: 'categoryCode',
              title: this.$t('物料类别'),
              minWidth: 140
            },
            {
              field: 'categoryName',
              title: this.$t('物料类别名称'),
              minWidth: 140
            },
            {
              field: 'materialCode',
              title: this.$t('物料编码'),
              minWidth: 140
            },
            {
              field: 'materialName',
              title: this.$t('物料名称'),
              minWidth: 140
            },
            {
              field: 'rebateType',
              title: this.$t('返利类型'),
              minWidth: 280,
              slots: {
                default: ({ row }) => {
                  const selectedItem = rebateTypeList.find((item) => item.value === row.rebateType)
                  const rebateTypeName = selectedItem?.text || null
                  return [<span>{rebateTypeName}</span>]
                }
              }
            },
            {
              field: 'untaxedAmt',
              title: isGf ? this.$t('返利金额含税（元）') : this.$t('返利金额未税（元）'),
              minWidth: 160
            },
            {
              field: 'rebateRatio',
              title: this.$t('返利比例（%）'),
              minWidth: 140
            },
            {
              field: 'priceDiff',
              title: this.$t('单片差价')
            },
            {
              field: 'stepRank',
              title: this.$t('阶梯等级'),
              slots: {
                default: ({ row, column }) => {
                  const isStep = [2, 3, 6, 7].includes(row.rebateType)
                  return [
                    <div>
                      <a
                        v-show={isStep}
                        style='color: #409eff'
                        on-click={() => this.handleClickCellTitle(row, column)}>
                        {this.$t('阶梯等级')}
                      </a>
                      <span v-show={!isStep}>-</span>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'payCashRatio',
              title: this.$t('付现比例')
            },
            {
              field: 'rebateFreq',
              title: this.$t('返利计算频次'),
              minWidth: 140,
              slots: {
                default: ({ row }) => {
                  const selectedItem = rebateCalculationFrequencyList.find(
                    (item) => item.value === row.rebateFreq
                  )
                  const rebateFreqName = selectedItem?.text || null
                  return [<span>{rebateFreqName}</span>]
                }
              }
            },
            {
              field: 'actualStartDate',
              title: this.$t('实际返利起始时间'),
              minWidth: 160,
              slots: {
                default: ({ row }) => {
                  const actualStartDate = Number(row.actualStartDate)
                    ? dayjs(Number(row.actualStartDate)).format('YYYY-MM-DD')
                    : row.actualStartDate
                  return [<span>{actualStartDate}</span>]
                }
              }
            },
            {
              field: 'remark',
              title: this.$t('备注'),
              minWidth: 140
            }
          ]
        case 'purcahseAttachment':
        case 'supplierAttachment':
          return [
            {
              width: 50,
              type: 'checkbox'
            },
            {
              title: this.$t('序号'),
              width: 50,
              type: 'seq'
            },
            {
              field: 'attachmentName',
              title: this.$t('附件名称'),
              slots: {
                default: ({ row, column }) => {
                  return [
                    <div>
                      <a
                        style='display: block; color: #409eff'
                        on-click={() => this.handleClickCellTitle(row, column)}>
                        {row.attachmentName}
                      </a>
                      <a
                        class='cell-btn'
                        v-show={this.cellTools.includes('download')}
                        on-click={() => this.handleClickCellTool('download', row)}>
                        <i class='vxe-icon-download' />
                        {this.$t('下载')}
                      </a>
                      <a
                        class='cell-btn'
                        v-show={this.cellTools.includes('delete')}
                        on-click={() => this.handleClickCellTool('delete', row)}>
                        <i class='vxe-icon-delete' />
                        {this.$t('删除')}
                      </a>
                    </div>
                  ]
                }
              }
            },
            {
              field: 'attachmentSize',
              title: this.$t('附件大小'),
              slots: {
                default: ({ row }) => {
                  return [
                    <span>{Number(((row.attachmentSize / 1024) * 100) / 100).toFixed(2)}KB</span>
                  ]
                }
              }
            },
            {
              field: 'uploadUserName',
              title: this.$t('上传人')
            },
            {
              field: 'uploadTime',
              title: this.$t('上传时间'),
              slots: {
                default: ({ row }) => {
                  const uploadTime = row.uploadTime
                    ? dayjs(Number(row.uploadTime)).format('YYYY-MM-DD HH:mm:ss')
                    : null
                  return [<span>{uploadTime}</span>]
                }
              }
            }
          ]
        default:
          return []
      }
    }
  },
  mounted() {},
  methods: {
    getGfCompanyList() {
      if (!this.detailInfo?.companyCode) return
      this.$API.rebateFeedback
        .isGfCompany({
          companyCode: this.detailInfo.companyCode
        })
        .then((res) => {
          if (res.code === 200) {
            this.isGf = res.data
          }
        })
    },
    // 限制日期选择
    disabledDateMethod(e) {
      // 只能选择当前日期往后的每月5号
      const { date } = e
      const day = dayjs(date).get('date')
      return Date.now() - 86400000 >= new Date(date).getTime() || day !== 5
    },
    // 序列化列表
    serializeList(list) {
      list.forEach((item) => {
        if (item.actualStartDate && !isNaN(Number(item.actualStartDate))) {
          item.stepList = item.ladderInfo ? JSON.parse(item.ladderInfo) : []
        }
      })
      return list
    }
  }
}
