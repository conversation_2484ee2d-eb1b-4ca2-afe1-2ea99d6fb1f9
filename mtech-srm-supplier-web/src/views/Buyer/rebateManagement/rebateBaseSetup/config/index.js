import { i18n } from '@/main.js'
import Vue from 'vue'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
export default {
  components: {
    RemoteAutocomplete
  }
}

const columnData = [
  // {
  //   type: 'checkbox',
  //   width: 50,
  //   allowEditing: false,
  //   allowResizing: false,
  //   customAttributes: {
  //     class: 'sticky-col-0'
  //   }
  // },
  {
    field: 'companyCode',
    headerText: i18n.t('公司代码'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('公司代码')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            remoteSearch={true}
            v-model={scoped.companyCode}
            operator='in'
            fields={{ text: 'orgName', value: 'orgCode' }}
            url={'/masterDataManagement/tenant/organization/specified-level-paged-query'}
            params={{
              organizationLevelCodes: ['ORG02', 'ORG01'],
              orgType: 'ORG001PRO',
              includeItself: true
            }}
            titleSwitch={false}
            multiple={false}
            placeholder={i18n.t('请选择公司')}
            popup-width={250}
            selectType='administrativeCompany'
            getPopupContainer={() => document.body}
            onChange={(e) => {
              if (e.itemData?.orgCode || e.value == null) {
                scoped.companyCode = e.itemData?.orgCode
                scoped.companyName = e.itemData?.orgName
              }
            }}></RemoteAutocomplete>
        </div>
      )
    }
  },
  {
    field: 'companyName',
    headerText: i18n.t('所属公司'),
    providedEditor: true,
    editorParams: {
      type: 'input',
      disabled: true
    }
  },
  {
    field: 'bookkeepingCode',
    headerText: i18n.t('记账码'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('记账码')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      let _dataSource = [
        { text: i18n.t('21'), value: '21', cssClass: '' },
        { text: i18n.t('50'), value: '50', cssClass: '' },
        { text: i18n.t('40'), value: '40', cssClass: '' }
      ]

      return (
        <div>
          <mt-select
            v-model={scoped.bookkeepingCode}
            fields={{ text: 'text', value: 'value' }}
            dataSource={_dataSource}
          />
        </div>
      )
    }
  },
  {
    field: 'origCurrencyCode',
    headerText: i18n.t('原币'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('原币')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            v-model={scoped.origCurrencyCode}
            url='/masterDataManagement/tenant/currency/queryAll'
            multiple={false}
            placeholder={i18n.t('请选择原币')}
            popup-width='250'
            fields={{ text: 'currencyName', value: 'currencyCode' }}
            search-fields={['currencyCode', 'currencyName']}
            records-position='data'></RemoteAutocomplete>
        </div>
      )
    }
  },
  {
    field: 'costCurrency',
    headerText: i18n.t('本位币'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('本位币')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            v-model={scoped.costCurrency}
            url='/masterDataManagement/tenant/currency/queryAll'
            multiple={false}
            placeholder={i18n.t('请选择本位币')}
            popup-width='250'
            fields={{ text: 'currencyName', value: 'currencyCode' }}
            search-fields={['currencyCode', 'currencyName']}
            records-position='data'></RemoteAutocomplete>
        </div>
      )
    }
  },
  {
    field: 'accountingSubjectCode',
    headerText: i18n.t('会计科目'),
    editorRender(h, scoped) {
      return (
        <div>
          <mt-input
            v-model={scoped.accountingSubjectCode}
            placeholder={i18n.t('请输入会计科目')}
            show-clear-button={true}></mt-input>
        </div>
      )
    }
  },
  {
    field: 'factoryCode',
    headerText: i18n.t('工厂代码'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('工厂代码')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <RemoteAutocomplete
            remoteSearch={true}
            v-model={scoped.factoryCode}
            operator='in'
            url={`/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
              'currentBu'
            )}`}
            fields={{ text: 'siteName', value: 'siteCode' }}
            searchFields={['siteName', 'siteCode']}
            disabled={scoped.voucherStatus == 2}
            multiple={false}
            placeholder={i18n.t('请选择工厂')}
            selectType='factoryAddress'
            getPopupContainer={() => document.body}
            onChange={(e) => {
              if (e.itemData?.siteCode || e.value == null) {
                scoped.factoryCode = e.itemData?.siteCode
                scoped.factoryName = e.itemData?.siteName
              }
            }}></RemoteAutocomplete>
        </div>
      )
    }
  },
  // {
  //   field: 'factoryName',
  //   headerText: i18n.t('工厂名称'),
  //   providedEditor: true,
  //   editorParams: {
  //     type: 'input',
  //     disabled: true
  //   }
  // },
  {
    field: 'profitCenterCode',
    headerText: i18n.t('利润中心'),
    headerTemplate: () => {
      return {
        template: Vue.component('autoCalcIndexTypeTemplate', {
          template: `
              <div class="headers">
                <span style="color: red">*</span>
                <span class="e-headertext">{{$t('利润中心')}}</span>
              </div>
            `
        })
      }
    },
    editorRender(h, scoped) {
      return (
        <div>
          <mt-input
            v-model={scoped.profitCenterCode}
            placeholder={i18n.t('请输入利润中心')}
            show-clear-button={true}></mt-input>
        </div>
      )
    }
  },
  {
    field: 'accountingType',
    headerText: i18n.t('会计类型'),
    valueConverter: {
      type: 'map',
      map: {
        INCLUDED_TAX: i18n.t('含税'),
        NINCLUDED_TAX: i18n.t('不含税'),
        TAX_MONEY: i18n.t('税'),
        null: i18n.t('-')
      }
    },
    editorRender(h, scoped) {
      let _dataSource = [
        { text: i18n.t('含税'), value: 'INCLUDED_TAX', cssClass: '' },
        { text: i18n.t('不含税'), value: 'NINCLUDED_TAX', cssClass: '' },
        { text: i18n.t('税'), value: 'TAX_MONEY', cssClass: '' }
      ]

      return (
        <div>
          <mt-select
            v-model={scoped.accountingType}
            fields={{ text: 'text', value: 'value' }}
            dataSource={_dataSource}
          />
        </div>
      )
    }
  },
  {
    field: 'paymentTerms',
    headerText: i18n.t('付款条件'),
    editorRender(h, scoped) {
      return (
        <div>
          <mt-input
            v-model={scoped.paymentTerms}
            placeholder={i18n.t('请输入付款条件')}
            show-clear-button={true}></mt-input>
        </div>
      )
    }
  },

  {
    field: 'enableFlag',
    headerText: i18n.t('是否启用'),
    allowEditing: false,
    valueConverter: {
      type: 'map',
      map: [
        {
          status: 'N',
          label: i18n.t('停用'),
          cssClass: ['status-label', 'status-enable']
        },
        {
          status: 'Y',
          label: i18n.t('启用'),
          cssClass: ['status-label', 'status-disable']
        },
        {
          status: '',
          label: i18n.t('-'),
          cssClass: ['status-label', 'status-disable']
        }
      ],
      fields: { text: 'label', value: 'status' }
    },
    fields: { text: 'label', value: 'status' },
    cellTools: [
      {
        id: 'Enable',
        icon: 'icon_list_enable',
        title: i18n.t('启用'),
        visibleCondition: (data) => {
          return data['enableFlag'] == 'N'
        }
      },
      {
        id: 'Disable',
        icon: 'icon_list_disable',
        title: i18n.t('停用'),
        visibleCondition: (data) => {
          return data['enableFlag'] == 'Y'
        }
      }
    ]
  }
]

export const pageConfig = [
  {
    gridId: '82537b16-863f-4af8-800c-c6f51547ec3c',
    title: i18n.t('返利基础设置'),
    isUseCustomSearch: true,
    isUseCustomEditor: true,
    isCustomSearchRules: true,
    buttonQuantity: 7,
    toolbar: {
      useBaseConfig: false,
      tools: [
        [
          {
            id: 'Add',
            icon: 'icon_solid_Createorder',
            title: i18n.t('新增')
          },
          'Delete'
        ],
        ['Filter', 'Refresh', 'Setting']
      ]
    },
    useToolTemplate: false,
    grid: {
      editSettings: {
        allowAdding: true,
        allowEditing: true,
        allowDeleting: true,
        mode: 'Normal', // 默认normal模式
        allowEditOnDblClick: true,
        showConfirmDialog: false,
        showDeleteConfirmDialog: false,
        newRowPosition: 'Top'
      },
      virtualPageSize: 30,
      enableVirtualization: true,
      customSelection: true, // 使用自定义勾选列
      showSelected: false,
      columnData,
      asyncConfig: {
        url: 'analysis/tenant/siteProfitCenterRelation/pageQuery'
      }
    }
  }
]
