/**
 * 返利反馈
 */
import { API } from '@mtech-common/http'
import qs from 'qs'
const NAME = 'rebateFeedback'
const PROXY_BASE = '/analysis/tenant'

const APIS = {
  /******** 供方-返利协议确认 *********/
  // 列表-导出
  exportRacList: (data = {}) =>
    API.post(`${PROXY_BASE}/rebateHeader/exportHeader`, data, {
      responseType: 'blob'
    }),
  // 明细-根据id获取明细信息
  getRacDetailById: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/getDetail`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
  },
  // 明细头部-协议模板下拉列表
  queryAgreemenTempalteList: (data) => {
    return API.post(`${PROXY_BASE}/rebateAgreementTemplate/queryTemplate`, data)
  },
  // 明细-打印
  printRacDetail: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/agreementPrint`, qs.stringify(data), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob'
    })
  },
  // 明细-保存
  saveRacDetail: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/supplierSaveVoucher`, data)
  },
  // 明细-提交
  submitRacDetail: (data) => {
    return API.post(`${PROXY_BASE}/rebateHeader/supplierSubmitVoucher`, data)
  },
  // 明细-判断是否为光伏
  isGfCompany: ({companyCode}) => {
    return API.post(`${PROXY_BASE}/rebateHeader/queryIsPvCompany/${companyCode}`, {})
  },

  /******** 供方-返利金额确认 *********/
  // 明细头部-打印
  printRebateAmountConfirmDetail: ({ rebateCode, startDate, endDate }) => {
    return API.get(
      `${PROXY_BASE}/rebateAmountItem/supplierAmountItemPrint/${rebateCode}/${startDate}/${endDate}`,
      {},
      { responseType: 'blob' }
    )
  }
}

export default {
  NAME,
  APIS
}
