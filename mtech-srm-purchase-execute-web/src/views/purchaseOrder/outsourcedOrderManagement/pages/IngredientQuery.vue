<!-- 委外订单原料查询 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="false"
      grid-id="5c11c4f9-0a54-4ba3-9d45-422d83ef00df"
      search-grid-id="be8073ec-e703-4280-99aa-edb10a71121f"
      @search="handleSearch"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getSearchFormItems } from '../config/searchForm'
import { columnData, toolbar } from '../config/index'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      requiredConditions: ['siteCode'],
      columns: columnData,
      toolbar
    }
  },
  computed: {
    searchConditions() {
      const items = getSearchFormItems()
      const dateFields = ['createTime', 'updateTime']
      dateFields.forEach((field) => {
        const item = items.find((item) => item.field === field)
        if (item) {
          item.onChange = this.handleDateTimeChange
        }
      })
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearch({ searchForm }) {
      this.searchForm = searchForm
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.handleSearch()
    },
    async getTableData() {
      const params = {
        ...this.searchForm
      }
      this.$refs.commonListRef.setLoading(true)
      try {
        const res = await this.$API.outsourcedOrderManagement.pageQueryIngredientApi(params)
        if (res.code === 200) {
          const records = res.data || []
          this.$refs.commonListRef.setTableData(records)
        } else {
          this.$toast({ content: res?.msg || this.$t('查询失败'), type: 'error' })
          this.$refs.commonListRef.setTableData([])
        }
      } catch (error) {
        console.error('查询委外订单原料失败:', error)
        this.$toast({ content: error?.msg || this.$t('查询失败'), type: 'error' })
        this.$refs.commonListRef.setTableData([])
      } finally {
        this.$refs.commonListRef.setLoading(false)
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        const res = await this.$API.outsourcedOrderManagement.exportIngredientApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
