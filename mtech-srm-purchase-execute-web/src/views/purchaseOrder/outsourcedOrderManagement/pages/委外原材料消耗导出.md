

## 委外原材料消耗导出-采方


**接口地址**:`/api/srm-purchase-execute/tenant/outsourced/material/consumption/buyer/export`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "buyerOrgCode": "",
  "buyerOrgName": "",
  "categoryCode": "",
  "categoryName": "",
  "companyCode": "",
  "companyName": "",
  "createTimeE": 0,
  "createTimeS": 0,
  "itemCode": "",
  "itemCodeList": [],
  "itemName": "",
  "itemVoucherDateE": 0,
  "itemVoucherDateS": 0,
  "itemVoucherYear": 0,
  "lineNo": 0,
  "orderCode": "",
  "page": {
    "asc": [],
    "ascs": [],
    "countId": "",
    "current": 0,
    "desc": [],
    "descs": [],
    "hitCount": true,
    "maxLimit": 0,
    "optimizeCountSql": true,
    "orders": [
      {
        "asc": true,
        "column": ""
      }
    ],
    "pages": 0,
    "records": [],
    "searchCount": true,
    "size": 0,
    "total": 0
  },
  "proItemCode": "",
  "proItemName": "",
  "receiveCode": "",
  "receiveItemNo": "",
  "siteCodeList": [],
  "stockSiteCodeList": [],
  "supplierCodeList": [],
  "supplierTenantId": 0,
  "transType": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|req|req|body|true|RawMaterialConsumptionQueryReq|RawMaterialConsumptionQueryReq|
|&emsp;&emsp;buyerOrgCode|采购组编码||false|string||
|&emsp;&emsp;buyerOrgName|采购组名称||false|string||
|&emsp;&emsp;categoryCode|品类编码||false|string||
|&emsp;&emsp;categoryName|品类名称||false|string||
|&emsp;&emsp;companyCode|公司编号||false|string||
|&emsp;&emsp;companyName|公司名称||false|string||
|&emsp;&emsp;createTimeE|创建时间结束||false|integer(int64)||
|&emsp;&emsp;createTimeS|创建时间开始||false|integer(int64)||
|&emsp;&emsp;itemCode|原材料物料编码||false|string||
|&emsp;&emsp;itemCodeList|||false|array|string|
|&emsp;&emsp;itemName|原材料物料名称||false|string||
|&emsp;&emsp;itemVoucherDateE|物料凭证结束日期||false|integer(int64)||
|&emsp;&emsp;itemVoucherDateS|物料凭证开始日期||false|integer(int64)||
|&emsp;&emsp;itemVoucherYear|物料凭证年度||false|integer(int32)||
|&emsp;&emsp;lineNo|订单行号||false|integer(int32)||
|&emsp;&emsp;orderCode|订单号||false|string||
|&emsp;&emsp;page|分页||false|Page|Page|
|&emsp;&emsp;&emsp;&emsp;asc|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;ascs|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;countId|||false|string||
|&emsp;&emsp;&emsp;&emsp;current|||false|integer||
|&emsp;&emsp;&emsp;&emsp;desc|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;descs|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;hitCount|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;maxLimit|||false|integer||
|&emsp;&emsp;&emsp;&emsp;optimizeCountSql|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;orders|||false|array|OrderItem|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;asc|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;column|||false|string||
|&emsp;&emsp;&emsp;&emsp;pages|||false|integer||
|&emsp;&emsp;&emsp;&emsp;records|||false|array|object|
|&emsp;&emsp;&emsp;&emsp;searchCount|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;size|||false|integer||
|&emsp;&emsp;&emsp;&emsp;total|||false|integer||
|&emsp;&emsp;proItemCode|加工件物料编码||false|string||
|&emsp;&emsp;proItemName|加工件物料名称||false|string||
|&emsp;&emsp;receiveCode|物料凭证号||false|string||
|&emsp;&emsp;receiveItemNo|物料凭证行号||false|string||
|&emsp;&emsp;siteCodeList|工厂代码||false|array|string|
|&emsp;&emsp;stockSiteCodeList|库存地点编码||false|array|string|
|&emsp;&emsp;supplierCodeList|供应商编码||false|array|string|
|&emsp;&emsp;supplierTenantId|供应商租户Id||false|integer(int64)||
|&emsp;&emsp;transType|事务类型,543-消耗，544-取消消耗||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


暂无


**响应示例**:
```javascript

```