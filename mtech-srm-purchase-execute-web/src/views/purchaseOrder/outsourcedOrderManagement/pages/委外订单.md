

## 获取SAP委外订单-采方


**接口地址**:`/api/srm-purchase-execute/tenant/outsourced/order/buyer/query`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "itemCode": "",
  "orderCode": "",
  "orderItemNo": "",
  "siteCode": "",
  "supplierCode": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|req|req|body|true|KtOutsourcedOrderQueryReq|KtOutsourcedOrderQueryReq|
|&emsp;&emsp;itemCode|物料编码||false|string||
|&emsp;&emsp;orderCode|订单号||false|string||
|&emsp;&emsp;orderItemNo|订单行号||false|string||
|&emsp;&emsp;siteCode|工厂编码||false|string||
|&emsp;&emsp;supplierCode|供应商编码||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|通用返回统一封装对象«List«KtOutsourcedOrderQueryRes»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|响应编码|integer(int32)|integer(int32)|
|data|数据|array|KtOutsourcedOrderQueryRes|
|&emsp;&emsp;approvalStock|已审核未过账数量|number||
|&emsp;&emsp;basicUnitCode|基本单位|string||
|&emsp;&emsp;basicUnitName|基本单位名称|string||
|&emsp;&emsp;buyerGroupCode|采购组编码|string||
|&emsp;&emsp;buyerGroupName|采购组名称|string||
|&emsp;&emsp;categoryCode|品类编码|string||
|&emsp;&emsp;categoryName|品类名称|string||
|&emsp;&emsp;demandDate|需求日期|string||
|&emsp;&emsp;demandQuantity|需求量|number||
|&emsp;&emsp;inTheProcessQuantity|已建领料但未接收数量(流程中的数量)|number||
|&emsp;&emsp;itemCode|原材料物料编码|string||
|&emsp;&emsp;itemName|原材料物料名称|string||
|&emsp;&emsp;orderCode|关联采购订单号|string||
|&emsp;&emsp;orderItemNo|采购采购订单行号|integer(int32)||
|&emsp;&emsp;preQuantity|待领料数量|number||
|&emsp;&emsp;proBasicUnitCode|加工件基本单位|string||
|&emsp;&emsp;proBasicUnitName|加工件基本单位名称|string||
|&emsp;&emsp;proDemandQuantity|加工件需求量|number||
|&emsp;&emsp;proItemCode|加工件物料编码|string||
|&emsp;&emsp;proItemName|加工件物料名称|string||
|&emsp;&emsp;qualityTestStock|质量检验中的库存|number||
|&emsp;&emsp;roundingOff|最小包装量|number||
|&emsp;&emsp;sendQuantity|已发料数量|number||
|&emsp;&emsp;showStatus|用于前台展示订单状态|string||
|&emsp;&emsp;siteCode|工厂编码|string||
|&emsp;&emsp;siteName|工厂名称|string||
|&emsp;&emsp;siteStock|非限制库存|number||
|&emsp;&emsp;supplierCode|供应商编码|string||
|&emsp;&emsp;supplierName|供应商名称|string||
|&emsp;&emsp;supplierStock|供应商库存(分包库存）|number||
|&emsp;&emsp;unApprovalStock|未审核未过账数量|number||
|&emsp;&emsp;unDemandQuantity|未清需求数量|number||
|&emsp;&emsp;unQualifiedStock|待检不合格库存|number||
|&emsp;&emsp;warehouseCode|仓位编码|string||
|&emsp;&emsp;warehouseManagerCode|仓管员编码|string||
|errorStackTrace|错误堆栈信息|string||
|msg|提示信息|string||
|success|是否成功|boolean||
|traceId|追踪ID|string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": [
		{
			"approvalStock": 0,
			"basicUnitCode": "",
			"basicUnitName": "",
			"buyerGroupCode": "",
			"buyerGroupName": "",
			"categoryCode": "",
			"categoryName": "",
			"demandDate": "",
			"demandQuantity": 0,
			"inTheProcessQuantity": 0,
			"itemCode": "",
			"itemName": "",
			"orderCode": "",
			"orderItemNo": 0,
			"preQuantity": 0,
			"proBasicUnitCode": "",
			"proBasicUnitName": "",
			"proDemandQuantity": 0,
			"proItemCode": "",
			"proItemName": "",
			"qualityTestStock": 0,
			"roundingOff": 0,
			"sendQuantity": 0,
			"showStatus": "",
			"siteCode": "",
			"siteName": "",
			"siteStock": 0,
			"supplierCode": "",
			"supplierName": "",
			"supplierStock": 0,
			"unApprovalStock": 0,
			"unDemandQuantity": 0,
			"unQualifiedStock": 0,
			"warehouseCode": "",
			"warehouseManagerCode": ""
		}
	],
	"errorStackTrace": "",
	"msg": "",
	"success": true,
	"traceId": ""
}
```