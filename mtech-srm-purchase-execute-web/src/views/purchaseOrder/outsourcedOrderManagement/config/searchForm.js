/**
 * 委外订单管理搜索表单配置
 */
import { i18n } from '@/main.js'

export const getSearchFormItems = () => [
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('加工商'),
    field: 'supplierCode',
    type: 'remoteAutocomplete',
    props: {
      url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode']
    }
  },
  {
    label: i18n.t('委外订单号'),
    field: 'orderCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('委外订单行号'),
    field: 'orderItemNo',
    type: 'input'
  },
  {
    label: i18n.t('原材料物料编码'),
    field: 'itemCode',
    type: 'input'
  }
]

export const getConsSearchFormItems = () => [
  {
    label: i18n.t('物料凭证号'),
    field: 'receiveCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('物料凭证行号'),
    field: 'receiveItemNo',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('加工商'),
    field: 'supplierCode',
    type: 'remoteAutocomplete',
    props: {
      url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode']
    }
  },
  {
    label: i18n.t('委外订单号'),
    field: 'orderCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('原材料物料编码'),
    field: 'itemCode',
    type: 'input'
  },
  {
    label: i18n.t('原材料物料名称'),
    field: 'itemName',
    type: 'input'
  },
  {
    label: i18n.t('加工件物料编码'),
    field: 'proItemCode',
    type: 'input'
  },
  {
    label: i18n.t('加工件物料名称'),
    field: 'proItemName',
    type: 'input'
  },
  {
    label: i18n.t('事务类型'),
    field: 'transType',
    type: 'select',
    options: [
      { text: i18n.t('消耗'), value: '543' },
      { text: i18n.t('取消消耗'), value: '544' }
    ]
  },
  {
    label: i18n.t('采购组编码'),
    field: 'buyerOrgCode',
    type: 'input'
  },
  {
    label: i18n.t('采购组名称'),
    field: 'buyerOrgName',
    type: 'input'
  },
  {
    label: i18n.t('品类编码'),
    field: 'categoryCode',
    type: 'input'
  },
  {
    label: i18n.t('品类名称'),
    field: 'categoryName',
    type: 'input'
  },
  {
    label: i18n.t('库存地点编码'),
    field: 'stockSiteCode',
    type: 'input'
  },
  {
    label: i18n.t('物料凭证日期'),
    field: 'itemVoucherDate',
    type: 'dateRange'
  },
  {
    label: i18n.t('凭证创建时间'),
    field: 'createTime',
    type: 'dateRange'
  }
]
