import { i18n } from '@/main.js'

export const moveTypeOptions = [
  { text: i18n.t('消耗'), value: 0 },
  { text: i18n.t('取消消耗'), value: 1 }
]

// 工具栏按钮配置
export const toolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('委外订单号'),
    field: 'orderCode'
  },
  {
    title: i18n.t('委外订单行号'),
    field: 'orderItemNo'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName'
  },
  {
    title: i18n.t('加工商编码'),
    field: 'supplierCode'
  },
  {
    title: i18n.t('加工商名称'),
    field: 'supplierName'
  },
  {
    title: i18n.t('加工件物料编码'),
    field: 'proItemCode'
  },
  {
    title: i18n.t('加工件物料名称'),
    field: 'proItemName'
  },
  {
    title: i18n.t('加工件基本单位'),
    field: 'proBasicUnitCode'
  },
  {
    title: i18n.t('加工件基本单位名称'),
    field: 'proBasicUnitName'
  },
  {
    title: i18n.t('加工件需求量'),
    field: 'proDemandQuantity'
  },
  {
    title: i18n.t('原材料物料编码'),
    field: 'itemCode'
  },
  {
    title: i18n.t('原材料物料名称'),
    field: 'itemName'
  },
  {
    title: i18n.t('基本单位'),
    field: 'basicUnitCode'
  },
  {
    title: i18n.t('基本单位名称'),
    field: 'basicUnitName'
  },
  {
    title: i18n.t('采购组编码'),
    field: 'buyerGroupCode'
  },
  {
    title: i18n.t('采购组名称'),
    field: 'buyerGroupName'
  },
  {
    title: i18n.t('需求量'),
    field: 'demandQuantity'
  },
  {
    title: i18n.t('未清需求数量'),
    field: 'unDemandQuantity'
  },
  {
    title: i18n.t('待领料数量'),
    field: 'preQuantity'
  },
  {
    title: i18n.t('已建领料但未接收数量(流程中的数量)'),
    field: 'inTheProcessQuantity'
  },
  {
    title: i18n.t('已发料数量'),
    field: 'sendQuantity'
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName'
  },
  {
    title: i18n.t('需求日期'),
    field: 'demandDate'
  },
  {
    title: i18n.t('仓位编码'),
    field: 'warehouseCode'
  },
  {
    title: i18n.t('仓管员编码'),
    field: 'warehouseManagerCode'
  },
  {
    title: i18n.t('供应商库存(分包库存)'),
    field: 'supplierStock'
  },
  {
    title: i18n.t('非限制库存'),
    field: 'siteStock'
  },
  {
    title: i18n.t('待检不合格库存'),
    field: 'unQualifiedStock'
  },
  {
    title: i18n.t('质量检验中的库存'),
    field: 'qualityTestStock'
  },
  {
    title: i18n.t('已审核未过账数量'),
    field: 'approvalStock'
  },
  {
    title: i18n.t('未审核未过账数量'),
    field: 'unApprovalStock'
  },
  {
    title: i18n.t('最小包装量'),
    field: 'roundingOff'
  },
  {
    title: i18n.t('订单状态'),
    field: 'showStatus'
  }
]

export const consColumnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('物料凭证号'),
    field: 'receiveCode'
  },
  {
    title: i18n.t('物料凭证行号'),
    field: 'receiveItemNo'
  },
  {
    title: i18n.t('物料凭证年度'),
    field: 'itemVoucherYear'
  },
  {
    title: i18n.t('委外订单号'),
    field: 'orderCode'
  },
  {
    title: i18n.t('委外订单行号'),
    field: 'lineNo'
  },
  {
    title: i18n.t('工厂代码'),
    field: 'siteCode'
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName'
  },
  {
    title: i18n.t('加工商编码'),
    field: 'supplierCode'
  },
  {
    title: i18n.t('加工商名称'),
    field: 'supplierName'
  },
  {
    title: i18n.t('加工件物料编码'),
    field: 'proItemCode'
  },
  {
    title: i18n.t('加工件物料名称'),
    field: 'proItemName'
  },
  {
    title: i18n.t('加工件单位编码'),
    field: 'proUnitCode'
  },
  {
    title: i18n.t('加工件单位名称'),
    field: 'proUnitName'
  },
  {
    title: i18n.t('原材料物料编码'),
    field: 'itemCode'
  },
  {
    title: i18n.t('原材料物料名称'),
    field: 'itemName'
  },
  {
    title: i18n.t('原材料单位编码'),
    field: 'unitCode'
  },
  {
    title: i18n.t('原材料单位名称'),
    field: 'unitName'
  },
  {
    title: i18n.t('采购组编码'),
    field: 'buyerOrgCode'
  },
  {
    title: i18n.t('采购组名称'),
    field: 'buyerOrgName'
  },
  {
    title: i18n.t('事务类型'),
    field: 'transType',
    formatter: ({ cellValue }) => {
      const transTypeMap = {
        543: i18n.t('消耗'),
        544: i18n.t('取消消耗')
      }
      return transTypeMap[cellValue] || cellValue
    }
  },
  {
    title: i18n.t('数量'),
    field: 'quantity'
  },
  {
    title: i18n.t('品类编码'),
    field: 'categoryCode'
  },
  {
    title: i18n.t('品类名称'),
    field: 'categoryName'
  },
  {
    title: i18n.t('库存地点编码'),
    field: 'stockSiteCode'
  },
  {
    title: i18n.t('库存地点'),
    field: 'stockSite'
  },
  {
    title: i18n.t('物料凭证日期'),
    field: 'itemVoucherDate'
  },
  {
    title: i18n.t('更新时间'),
    field: 'updateTime'
  },
  {
    title: i18n.t('更新用户'),
    field: 'updateUserName'
  }
]
