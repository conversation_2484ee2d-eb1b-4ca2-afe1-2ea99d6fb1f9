

## 供应商结存库存-供方


**接口地址**:`/api/srm-purchase-execute/tenant/jc/inv/supplier/query`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "itemCode": "",
  "month": "",
  "siteCode": "",
  "supplierCode": "",
  "year": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|req|req|body|true|SupplierJcInvQueryReq|SupplierJcInvQueryReq|
|&emsp;&emsp;itemCode|物料编码||false|string||
|&emsp;&emsp;month|月份||false|string||
|&emsp;&emsp;siteCode|工厂编码||false|string||
|&emsp;&emsp;supplierCode|供应商编码||false|string||
|&emsp;&emsp;year|年份||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|通用返回统一封装对象«List«SupplierJcInvQueryRes»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|响应编码|integer(int32)|integer(int32)|
|data|数据|array|SupplierJcInvQueryRes|
|&emsp;&emsp;buyerGroupCode|采购组编码|string||
|&emsp;&emsp;buyerGroupName|采购组名称|string||
|&emsp;&emsp;categoryCode|品类编码|string||
|&emsp;&emsp;categoryName|品类名称|string||
|&emsp;&emsp;itemCode|物料编码|string||
|&emsp;&emsp;itemName|物料名称|string||
|&emsp;&emsp;month|月份|string||
|&emsp;&emsp;siteCode|工厂编码|string||
|&emsp;&emsp;siteName|工厂名称|string||
|&emsp;&emsp;supplierCode|供应商编码|string||
|&emsp;&emsp;supplierInv|供应商库存|number||
|&emsp;&emsp;supplierName|供应商名称|string||
|&emsp;&emsp;year|年份|string||
|errorStackTrace|错误堆栈信息|string||
|msg|提示信息|string||
|success|是否成功|boolean||
|traceId|追踪ID|string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": [
		{
			"buyerGroupCode": "",
			"buyerGroupName": "",
			"categoryCode": "",
			"categoryName": "",
			"itemCode": "",
			"itemName": "",
			"month": "",
			"siteCode": "",
			"siteName": "",
			"supplierCode": "",
			"supplierInv": 0,
			"supplierName": "",
			"year": ""
		}
	],
	"errorStackTrace": "",
	"msg": "",
	"success": true,
	"traceId": ""
}
```