<!-- 采方-加工商结存库存查询 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="false"
      grid-id="33fc9b92-0958-4e5f-bb25-86913f3cef3d"
      search-grid-id="4c3c2fc9-1fbc-4cda-89c8-63f0b1cf4d4b"
      @search="handleSearch"
      @templateSearch="handleTemplateSearch"
      @toolbarClick="handleToolbarClick"
    >
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getSearchFormItems } from './config/searchForm'
import { columnData, toolbar } from './config/index'
import { getHeadersFileName, download } from '@/utils/utils'

export default {
  name: 'BalanceInventory',
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      columns: columnData,
      toolbar: toolbar,
      requiredConditions: ['siteCode', 'year', 'month']
    }
  },
  computed: {
    searchConditions() {
      const items = getSearchFormItems()
      return items
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleSearch({ searchForm }) {
      this.searchForm = searchForm
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      this.searchForm = templateData
      this.handleSearch()
    },
    async getTableData() {
      const params = {
        ...this.searchForm
      }
      this.$refs.commonListRef.setLoading(true)
      try {
        // {{ Source: 查询接口.md - 根据新接口文档调整数据处理 }}
        const res = await this.$API.outsourcingNew.getBalanceInventoryApi(params)
        if (res?.success && res.code === 0) {
          const records = res.data || []
          this.$refs.commonListRef.setTableData(records)
        } else {
          this.$toast({ content: res?.msg || this.$t('查询失败'), type: 'error' })
          this.$refs.commonListRef.setTableData([])
        }
      } catch (error) {
        console.error('查询加工商结存库存失败:', error)
        this.$toast({ content: error?.message || this.$t('查询失败'), type: 'error' })
        this.$refs.commonListRef.setTableData([])
      } finally {
        this.$refs.commonListRef.setLoading(false)
      }
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          ...this.searchForm
        }

        // {{ Source: 导出接口.md - 使用新的导出接口 }}
        const res = await this.$API.outsourcingNew.exportBalanceInventoryApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error?.message || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    }
  }
}
</script>
