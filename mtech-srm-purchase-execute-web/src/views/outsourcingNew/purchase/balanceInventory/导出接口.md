

## 供应商结存库存导出-采方


**接口地址**:`/api/srm-purchase-execute/tenant/jc/inv/buyer/export`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "itemCode": "",
  "month": "",
  "siteCode": "",
  "supplierCode": "",
  "year": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|req|req|body|true|SupplierJcInvQueryReq|SupplierJcInvQueryReq|
|&emsp;&emsp;itemCode|物料编码||false|string||
|&emsp;&emsp;month|月份||false|string||
|&emsp;&emsp;siteCode|工厂编码||false|string||
|&emsp;&emsp;supplierCode|供应商编码||false|string||
|&emsp;&emsp;year|年份||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK||
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


暂无


**响应示例**:
```javascript

```