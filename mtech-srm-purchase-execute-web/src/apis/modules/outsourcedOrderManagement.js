import { API } from '@mtech-common/http'

export const NAME = 'outsourcedOrderManagement'

// 委外订单原料查询-获取SAP委外订单
export const pageQueryIngredientApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/order/buyer/query`, data)

// 委外订单原料查询-导出SAP委外订单
export const exportIngredientApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/order/buyer/export`, data, {
    responseType: 'blob'
  })

// 委外原料消耗查询-分页查询
export const pageQueryConsumptionApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/material/consumption/buyer/query`, data)

// 委外原料消耗查询-导出
export const exportConsumptionApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/material/consumption/buyer/export`, data, {
    responseType: 'blob'
  })

// 供方-委外原料消耗查询-分页查询
export const pageQueryConsumptionSupplierApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/material/consumption/supplier/query`, data)

// 供方-委外原料消耗查询-导出
export const exportConsumptionSupplierApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/material/consumption/supplier/export`, data, {
    responseType: 'blob'
  })

// 供方-委外订单原料查询-获取SAP委外订单
export const pageQueryIngredientSupplierApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/order/supplier/query`, data)

// 供方-委外订单原料查询-导出SAP委外订单
export const exportIngredientSupplierApi = (data = {}) =>
  API.post(`/srm-purchase-execute/tenant/outsourced/order/supplier/export`, data, {
    responseType: 'blob'
  })
